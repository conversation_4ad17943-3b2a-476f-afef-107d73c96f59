import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Task, TaskStatus, TaskPriority, CreateTaskRequest, UpdateTaskRequest } from '../types/task';
import taskService from '../services/taskService';

const TaskDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { taskId } = useParams<{ taskId: string }>();
  const isNewTask = taskId === 'new';
  
  const [loading, setLoading] = useState<boolean>(true);
  const [saving, setSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [farms, setFarms] = useState<Array<{ id: number; name: string }>>([]);
  const [users, setUsers] = useState<Array<{ id: number; name: string }>>([]);
  
  // Form state
  const [formData, setFormData] = useState<{
    title: string;
    description: string;
    status: TaskStatus;
    priority: TaskPriority;
    dueDate: string;
    assignedTo: number | null;
    farmId: number;
    fieldId?: number;
    equipmentId?: number;
  }>({
    title: '',
    description: '',
    status: TaskStatus.TODO,
    priority: TaskPriority.MEDIUM,
    dueDate: '',
    assignedTo: null,
    farmId: 0,
    fieldId: undefined,
    equipmentId: undefined
  });
  
  // Fetch task data on component mount
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Get farms
        const farmData = await window.electronAPI.getFarms();
        setFarms(farmData);
        
        // Set default farm ID if available
        if (farmData.length > 0 && formData.farmId === 0) {
          setFormData(prev => ({ ...prev, farmId: farmData[0].id }));
        }
        
        // For now, we'll use mock users
        // In a real app, this would be fetched from an API
        setUsers([
          { id: 1, name: 'John Doe' },
          { id: 2, name: 'Jane Smith' }
        ]);
        
        // If editing an existing task, fetch its data
        if (!isNewTask) {
          const taskData = await taskService.getTaskById(parseInt(taskId));
          if (taskData) {
            setFormData({
              title: taskData.title,
              description: taskData.description,
              status: taskData.status,
              priority: taskData.priority,
              dueDate: taskData.dueDate ? taskData.dueDate.toISOString().split('T')[0] : '',
              assignedTo: taskData.assignedTo,
              farmId: taskData.farmId,
              fieldId: taskData.fieldId,
              equipmentId: taskData.equipmentId
            });
          } else {
            setError('Task not found');
          }
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [isNewTask, taskId]);
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle number input changes
  const handleNumberInputChange = (e: React.ChangeEvent<HTMLSelectElement>, fieldName: string) => {
    const value = e.target.value === '' ? null : parseInt(e.target.value);
    setFormData(prev => ({ ...prev, [fieldName]: value }));
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    
    try {
      // Prepare task data
      const taskData = {
        title: formData.title,
        description: formData.description,
        status: formData.status,
        priority: formData.priority,
        dueDate: formData.dueDate ? new Date(formData.dueDate) : null,
        assignedTo: formData.assignedTo,
        farmId: formData.farmId,
        fieldId: formData.fieldId,
        equipmentId: formData.equipmentId
      };
      
      if (isNewTask) {
        // Create new task
        await taskService.createTask(taskData as CreateTaskRequest);
        window.electronAPI.showNotification('Success', 'Task created successfully');
      } else {
        // Update existing task
        await taskService.updateTask({
          id: parseInt(taskId),
          ...taskData
        } as UpdateTaskRequest);
        window.electronAPI.showNotification('Success', 'Task updated successfully');
      }
      
      // Navigate back to task list
      navigate('/tasks');
    } catch (err) {
      console.error('Error saving task:', err);
      setError('Failed to save task. Please try again.');
      window.electronAPI.showNotification('Error', 'Failed to save task');
    } finally {
      setSaving(false);
    }
  };
  
  // Handle task deletion
  const handleDelete = async () => {
    if (isNewTask) return;
    
    if (confirm('Are you sure you want to delete this task?')) {
      try {
        await taskService.deleteTask(parseInt(taskId));
        window.electronAPI.showNotification('Success', 'Task deleted successfully');
        navigate('/tasks');
      } catch (err) {
        console.error('Error deleting task:', err);
        setError('Failed to delete task. Please try again.');
        window.electronAPI.showNotification('Error', 'Failed to delete task');
      }
    }
  };
  
  // Handle cancel button
  const handleCancel = () => {
    navigate('/tasks');
  };
  
  if (loading) {
    return (
      <div className="p-6 text-center">
        <p>Loading task data...</p>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="p-6 text-center">
        <p className="text-red-500">{error}</p>
        <button
          onClick={() => navigate('/tasks')}
          className="mt-4 bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded"
        >
          Back to Tasks
        </button>
      </div>
    );
  }
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{isNewTask ? 'Create New Task' : 'Edit Task'}</h1>
        {!isNewTask && (
          <button
            onClick={handleDelete}
            className="bg-red-600 hover:bg-red-500 text-white px-4 py-2 rounded"
            disabled={saving}
          >
            Delete Task
          </button>
        )}
      </div>
      
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Title */}
            <div className="col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Title *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded"
                required
              />
            </div>
            
            {/* Description */}
            <div className="col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded"
                rows={4}
              />
            </div>
            
            {/* Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Status *
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded"
                required
              >
                {Object.values(TaskStatus).map(status => (
                  <option key={status} value={status}>
                    {status.replace('_', ' ')}
                  </option>
                ))}
              </select>
            </div>
            
            {/* Priority */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Priority *
              </label>
              <select
                name="priority"
                value={formData.priority}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded"
                required
              >
                {Object.values(TaskPriority).map(priority => (
                  <option key={priority} value={priority}>
                    {priority}
                  </option>
                ))}
              </select>
            </div>
            
            {/* Due Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Due Date
              </label>
              <input
                type="date"
                name="dueDate"
                value={formData.dueDate}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded"
              />
            </div>
            
            {/* Assigned To */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Assigned To
              </label>
              <select
                name="assignedTo"
                value={formData.assignedTo || ''}
                onChange={(e) => handleNumberInputChange(e, 'assignedTo')}
                className="w-full p-2 border border-gray-300 rounded"
              >
                <option value="">Unassigned</option>
                {users.map(user => (
                  <option key={user.id} value={user.id}>
                    {user.name}
                  </option>
                ))}
              </select>
            </div>
            
            {/* Farm */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Farm *
              </label>
              <select
                name="farmId"
                value={formData.farmId}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded"
                required
              >
                {farms.map(farm => (
                  <option key={farm.id} value={farm.id}>
                    {farm.name}
                  </option>
                ))}
              </select>
            </div>
            
            {/* Field (placeholder) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Field
              </label>
              <select
                name="fieldId"
                value={formData.fieldId || ''}
                onChange={(e) => handleNumberInputChange(e, 'fieldId')}
                className="w-full p-2 border border-gray-300 rounded"
              >
                <option value="">None</option>
                <option value="1">North Field</option>
                <option value="2">South Field</option>
              </select>
            </div>
            
            {/* Equipment (placeholder) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Equipment
              </label>
              <select
                name="equipmentId"
                value={formData.equipmentId || ''}
                onChange={(e) => handleNumberInputChange(e, 'equipmentId')}
                className="w-full p-2 border border-gray-300 rounded"
              >
                <option value="">None</option>
                <option value="1">Tractor</option>
                <option value="2">Harvester</option>
              </select>
            </div>
          </div>
          
          {/* Form Actions */}
          <div className="mt-8 flex justify-end space-x-4">
            <button
              type="button"
              onClick={handleCancel}
              className="bg-gray-300 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded"
              disabled={saving}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded"
              disabled={saving}
            >
              {saving ? 'Saving...' : isNewTask ? 'Create Task' : 'Update Task'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TaskDetailPage;