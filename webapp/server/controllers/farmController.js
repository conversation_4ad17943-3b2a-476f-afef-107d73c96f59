import Farm from '../models/Farm.js';
import User from '../models/User.js';
import FarmAdmin from '../models/FarmAdmin.js';
import UserFarm from '../models/UserFarm.js';
import Role from '../models/Role.js';
import SubscriptionPlan from '../models/SubscriptionPlan.js';
import DocumentFolder from '../models/DocumentFolder.js';
import { sequelize } from '../config/database.js';
import { isSubdomainAvailable, validateSubdomain } from '../utils/subdomainUtils.js';
import { validateFileType, generateStoragePath, saveFile } from '../utils/fileUtils.js';
import { uploadToSpaces } from '../utils/spacesUtils.js';
import { uuid_nil } from '../utils/uuidUtils.js';
import matrixClientService from '../services/matrixClientService.js';

// Helper function to find or create a role by name and farm ID
const findOrCreateRoleByName = async (roleName, farmId, transaction) => {
  try {
    // First, try to find an existing role for this farm with this name
    let role = await Role.findOne({
      where: {
        name: roleName,
        farm_id: farmId
      },
      transaction
    });

    // If role exists, return it
    if (role) {
      return role;
    }

    // If no farm-specific role exists, try to find a global role with this name
    role = await Role.findOne({
      where: {
        name: roleName,
        farm_id: null
      },
      transaction
    });

    // If global role exists, return it
    if (role) {
      return role;
    }

    // If no role exists at all, create a new one for this farm
    console.log(`Creating new role '${roleName}' for farm ${farmId}`);
    role = await Role.create({
      name: roleName,
      farm_id: farmId,
      description: `${roleName} role for farm ${farmId}`,
      is_system_role: true
    }, { transaction });

    return role;
  } catch (error) {
    console.error(`Error finding or creating role '${roleName}' for farm ${farmId}:`, error);
    throw error;
  }
};

// Get all farms (admin only)
export const getAllFarms = async (req, res) => {
  try {
    const farms = await Farm.findAll({
      include: [
        {
          model: SubscriptionPlan,
          attributes: ['name', 'price_monthly', 'price_yearly']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    return res.status(200).json({ farms });
  } catch (error) {
    console.error('Error getting farms:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single farm by ID
export const getFarmById = async (req, res) => {
  try {
    const { farmId } = req.params;

    console.log(`[getFarmById] Attempting to find farm with ID: ${farmId}`);
    console.log(`[getFarmById] Request path: ${req.path}`);
    console.log(`[getFarmById] Request method: ${req.method}`);
    console.log(`[getFarmById] Request hostname: ${req.hostname}`);

    const farm = await Farm.findByPk(farmId, {
      include: [
        {
          model: SubscriptionPlan,
          attributes: ['name', 'price_monthly', 'price_yearly', 'features', 'max_farms', 'max_users']
        },
        {
          model: User,
          attributes: { 
            exclude: [
              'password_hash', 
              'two_factor_secret',
              'two_factor_method',
              'phone_verification_code',
              'phone_verification_expires',
              'reset_password_token',
              'reset_password_expires',
              'email_verification_token',
              'email_verification_expires',
              'email_2fa_code',
              'email_2fa_expires',
              'matrix_token'
            ] 
          },
          through: { attributes: ['role'] }
        }
      ]
    });

    if (!farm) {
      console.log(`[getFarmById] Farm with ID ${farmId} not found in database`);
      return res.status(404).json({ error: 'Farm not found' });
    }

    console.log(`[getFarmById] Successfully found farm with ID: ${farmId}`);
    return res.status(200).json({ farm });
  } catch (error) {
    console.error(`[getFarmById] Error getting farm with ID ${req.params.farmId}:`, error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new farm
export const createFarm = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      name, 
      subscription_plan_id, 
      billing_email,
      billing_address,
      billing_city,
      billing_state,
      billing_zip_code,
      billing_country,
      user_id,
      subdomain,
      custom_login_text,
      custom_login_logo,
      location_data,
      tax_rate,
      customer_portal_enabled
    } = req.body;

    // Validate required fields
    if (!name) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Farm name is required' });
    }

    if (!user_id) {
      await transaction.rollback();
      return res.status(400).json({ error: 'User ID is required to create a farm' });
    }

    // Find user to ensure they exist
    const user = await User.findByPk(user_id);
    if (!user) {
      await transaction.rollback();
      return res.status(404).json({ error: 'User not found' });
    }

    // If subscription plan is provided, ensure it exists
    if (subscription_plan_id) {
      const plan = await SubscriptionPlan.findByPk(subscription_plan_id);
      if (!plan) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Subscription plan not found' });
      }
    }

    // Validate subdomain if provided
    if (subdomain) {
      const validation = validateSubdomain(subdomain);
      if (!validation.isValid) {
        await transaction.rollback();
        return res.status(400).json({ error: validation.message });
      }

      // Check if subdomain is available
      const available = await isSubdomainAvailable(subdomain);
      if (!available) {
        await transaction.rollback();
        return res.status(400).json({ error: 'This subdomain is already taken' });
      }
    }

    // Create farm
    const farm = await Farm.create({
      name,
      subscription_plan_id,
      subscription_status: subscription_plan_id ? 'active' : 'pending',
      subscription_start_date: subscription_plan_id ? new Date() : null,
      billing_email,
      billing_address,
      billing_city,
      billing_state,
      billing_zip_code,
      billing_country,
      subdomain,
      custom_login_text,
      custom_login_logo,
      location_data,
      tax_rate,
      customer_portal_enabled: customer_portal_enabled !== undefined ? customer_portal_enabled : false
    }, { transaction });

    // Associate user with farm as admin
    await FarmAdmin.create({
      farm_id: farm.id,
      user_id,
      role: 'admin'
    }, { transaction });

    // Create UserFarm association (farm owners are automatically approved)
    // Find or create the farm_owner role
    const ownerRole = await findOrCreateRoleByName('farm_owner', farm.id, transaction);

    const userFarm = await UserFarm.create({
      user_id,
      farm_id: farm.id,
      role: 'farm_owner',
      role_id: ownerRole.id,
      is_approved: true
    }, { transaction });

    // Update menu preferences based on the assigned role
    try {
      console.log('Updating menu preferences based on farm_owner role');
      // Import the menu utility function
      const { updateMenuPreferencesByRole } = await import('../utils/menuUtils.js');

      // Update menu preferences with the utility function
      await updateMenuPreferencesByRole(user_id, ownerRole, transaction);
      console.log('Menu preferences updated with role-specific items');
    } catch (menuUpdateError) {
      console.error('Error updating menu preferences with role-specific items:', menuUpdateError);
      // Don't fail the transaction if this fails
    }

    // Ensure the user exists in Matrix
    try {
      console.log(`Ensuring user ${user_id} exists in Matrix after farm creation...`);
      await matrixClientService.ensureMatrixUser(user_id);
      console.log(`User ${user_id} successfully created/verified in Matrix after farm creation`);
    } catch (matrixError) {
      console.error('Error ensuring user exists in Matrix after farm creation:', matrixError);
      // Don't fail the transaction if Matrix creation fails
    }

    // Create a root folder for the farm
    await DocumentFolder.create({
      name: `${farm.name} Root`,
      description: `Root folder for ${farm.name}`,
      parent_folder_id: uuid_nil(),
      farm_id: farm.id,
      created_by: user_id
    }, { transaction });

    // Create a folder in Digital Ocean Spaces for the farm
    try {
      // Create an empty file to establish the farm's root folder in Spaces
      const emptyBuffer = Buffer.from('');
      const folderPath = `${farm.id}/.folder`;
      await uploadToSpaces(emptyBuffer, folderPath);
      console.log(`Created folder in Digital Ocean Spaces for farm ${farm.id}`);
    } catch (error) {
      console.error(`Error creating folder in Digital Ocean Spaces for farm ${farm.id}:`, error);
      // Don't fail the farm creation if the folder creation fails
    }

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Farm created successfully',
      farm 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating farm:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a farm
export const updateFarm = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { 
      name, 
      subscription_plan_id, 
      subscription_status,
      subscription_start_date,
      subscription_end_date,
      billing_email,
      billing_address,
      billing_city,
      billing_state,
      billing_zip_code,
      billing_country,
      payment_method_id,
      stripe_customer_id,
      custom_login_text,
      custom_login_logo,
      subdomain,
      location_data,
      tax_rate,
      customer_portal_enabled
    } = req.body;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // If subscription plan is changed, ensure it exists
    if (subscription_plan_id && subscription_plan_id !== farm.subscription_plan_id) {
      const plan = await SubscriptionPlan.findByPk(subscription_plan_id);
      if (!plan) {
        return res.status(404).json({ error: 'Subscription plan not found' });
      }
    }

    // Check subdomain if it's being updated and is different from the current one
    if (subdomain && subdomain !== farm.subdomain) {
      // Validate subdomain format
      const validation = validateSubdomain(subdomain);
      if (!validation.isValid) {
        return res.status(400).json({ error: validation.message });
      }

      // Check if subdomain is available (excluding the current farm)
      const available = await isSubdomainAvailable(subdomain, farmId);
      if (!available) {
        return res.status(400).json({ error: 'This subdomain is already taken' });
      }
    }
    // If subdomain is the same as the current one, no need to validate or check availability

    // Update farm
    await farm.update({
      name: name !== undefined ? name : farm.name,
      subscription_plan_id: subscription_plan_id !== undefined ? subscription_plan_id : farm.subscription_plan_id,
      subscription_status: subscription_status !== undefined ? subscription_status : farm.subscription_status,
      subscription_start_date: subscription_start_date !== undefined ? subscription_start_date : farm.subscription_start_date,
      subscription_end_date: subscription_end_date !== undefined ? subscription_end_date : farm.subscription_end_date,
      billing_email: billing_email !== undefined ? billing_email : farm.billing_email,
      billing_address: billing_address !== undefined ? billing_address : farm.billing_address,
      billing_city: billing_city !== undefined ? billing_city : farm.billing_city,
      billing_state: billing_state !== undefined ? billing_state : farm.billing_state,
      billing_zip_code: billing_zip_code !== undefined ? billing_zip_code : farm.billing_zip_code,
      billing_country: billing_country !== undefined ? billing_country : farm.billing_country,
      payment_method_id: payment_method_id !== undefined ? payment_method_id : farm.payment_method_id,
      stripe_customer_id: stripe_customer_id !== undefined ? stripe_customer_id : farm.stripe_customer_id,
      custom_login_text: custom_login_text !== undefined ? custom_login_text : farm.custom_login_text,
      custom_login_logo: custom_login_logo !== undefined ? custom_login_logo : farm.custom_login_logo,
      subdomain: subdomain !== undefined ? subdomain : farm.subdomain,
      location_data: location_data !== undefined ? location_data : farm.location_data,
      tax_rate: tax_rate !== undefined ? tax_rate : farm.tax_rate,
      customer_portal_enabled: customer_portal_enabled !== undefined ? customer_portal_enabled : farm.customer_portal_enabled
    });

    return res.status(200).json({ 
      message: 'Farm updated successfully',
      farm 
    });
  } catch (error) {
    console.error('Error updating farm:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a farm (admin only)
export const deleteFarm = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Delete all UserFarm associations for this farm
    await UserFarm.destroy({
      where: { farm_id: farmId },
      transaction
    });

    // Delete farm
    await farm.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({ 
      message: 'Farm deleted successfully' 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting farm:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Add a user to a farm as admin
export const addFarmAdmin = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { userId, role } = req.body;

    // Validate required fields
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Find user to ensure they exist
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if user is already an admin for this farm
    const existingAdmin = await FarmAdmin.findOne({
      where: {
        farm_id: farmId,
        user_id: userId
      }
    });

    if (existingAdmin) {
      // Update role if user is already an admin
      await existingAdmin.update({ role: role || 'admin' });
      return res.status(200).json({ 
        message: 'Farm admin role updated successfully' 
      });
    }

    // Add user as farm admin
    await FarmAdmin.create({
      farm_id: farmId,
      user_id: userId,
      role: role || 'admin'
    });

    // Create UserFarm association if it doesn't exist
    const userFarm = await UserFarm.findOne({
      where: {
        user_id: userId,
        farm_id: farmId
      }
    });

    if (!userFarm) {
      // Find or create the role
      const roleName = role || 'farm_admin';
      const userRole = await findOrCreateRoleByName(roleName, farmId);

      const userFarm = await UserFarm.create({
        user_id: userId,
        farm_id: farmId,
        role: roleName,
        role_id: userRole.id,
        is_approved: true
      });

      // Update menu preferences based on the assigned role
      try {
        console.log(`Updating menu preferences based on ${roleName} role`);
        // Import the menu utility function
        const { updateMenuPreferencesByRole } = await import('../utils/menuUtils.js');

        // Update menu preferences with the utility function
        await updateMenuPreferencesByRole(userId, userRole);
        console.log('Menu preferences updated with role-specific items');
      } catch (menuUpdateError) {
        console.error('Error updating menu preferences with role-specific items:', menuUpdateError);
        // Don't fail the operation if this fails
      }
    }

    // Ensure the user exists in Matrix
    try {
      console.log(`Ensuring user ${userId} exists in Matrix after adding as farm admin...`);
      await matrixClientService.ensureMatrixUser(userId);
      console.log(`User ${userId} successfully created/verified in Matrix after adding as farm admin`);
    } catch (matrixError) {
      console.error('Error ensuring user exists in Matrix after adding as farm admin:', matrixError);
      // Don't fail the operation if Matrix creation fails
    }

    return res.status(201).json({ 
      message: 'User added as farm admin successfully' 
    });
  } catch (error) {
    console.error('Error adding farm admin:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Remove a user from farm admins
export const removeFarmAdmin = async (req, res) => {
  try {
    const { farmId, userId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Find user to ensure they exist
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Find farm admin relationship
    const farmAdmin = await FarmAdmin.findOne({
      where: {
        farm_id: farmId,
        user_id: userId
      }
    });

    if (!farmAdmin) {
      return res.status(404).json({ error: 'User is not an admin for this farm' });
    }

    // Check if this is the last admin
    const adminCount = await FarmAdmin.count({
      where: { farm_id: farmId }
    });

    if (adminCount <= 1) {
      return res.status(400).json({ error: 'Cannot remove the last admin of a farm' });
    }

    // Remove farm admin relationship
    await farmAdmin.destroy();

    return res.status(200).json({ 
      message: 'User removed from farm admins successfully' 
    });
  } catch (error) {
    console.error('Error removing farm admin:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all users for a farm
export const getFarmUsers = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all users associated with the farm through UserFarm
    const userFarms = await UserFarm.findAll({
      where: { farm_id: farmId },
      include: [{
        model: User,
        attributes: ['id', 'email', 'first_name', 'last_name', 'created_at', 'user_type', 'phone_number']
      }]
    });

    // Extract user data from the userFarms results
    const users = userFarms.map(userFarm => userFarm.User);

    // Get all farm admins
    const admins = await FarmAdmin.findAll({
      where: { farm_id: farmId },
      attributes: ['user_id', 'role']
    });

    // Create a map of user_id to admin role
    const adminRoles = {};
    admins.forEach(admin => {
      adminRoles[admin.user_id] = admin.role;
    });

    // Add admin role to user objects
    const usersWithRoles = users.map(user => {
      const userData = user.toJSON();
      userData.is_admin = !!adminRoles[user.id];
      userData.admin_role = adminRoles[user.id] || null;
      return userData;
    });

    return res.status(200).json({ users: usersWithRoles });
  } catch (error) {
    console.error('Error getting farm users:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Check if a subdomain is available
export const checkSubdomainAvailability = async (req, res) => {
  try {
    const { subdomain } = req.params;

    // Validate the subdomain format
    const validation = validateSubdomain(subdomain);
    if (!validation.isValid) {
      return res.status(400).json({
        available: false,
        message: validation.message
      });
    }

    // Import the withDatabaseRetry utility
    const { withDatabaseRetry } = await import('../utils/retryUtils.js');

    // Check if the subdomain is available with retry logic
    const available = await withDatabaseRetry(
      async () => {
        return await isSubdomainAvailable(subdomain);
      },
      {
        onRetry: (error, attempt, maxRetries) => {
          console.warn(`Database connection error when checking subdomain availability (attempt ${attempt}/${maxRetries}):`, error.message);
        }
      }
    );

    return res.status(200).json({
      available,
      message: available 
        ? 'This subdomain is available' 
        : 'This subdomain is already taken'
    });
  } catch (error) {
    console.error('Error checking subdomain availability:', error);
    return res.status(500).json({
      available: false,
      message: 'Error checking subdomain availability'
    });
  }
};

// Get farm details by subdomain (public endpoint for login page)
export const getFarmBySubdomain = async (req, res) => {
  try {
    const { subdomain } = req.params;

    // Validate the subdomain format
    const validation = validateSubdomain(subdomain);
    if (!validation.isValid) {
      return res.status(400).json({
        error: validation.message
      });
    }

    // Import the withDatabaseRetry utility
    const { withDatabaseRetry } = await import('../utils/retryUtils.js');

    // Find the farm by subdomain with retry logic
    const farm = await withDatabaseRetry(
      async () => {
        return await Farm.findOne({
          where: { subdomain },
          attributes: ['id', 'name', 'subdomain', 'custom_login_text', 'custom_login_logo', 'customer_portal_enabled']
        });
      },
      {
        onRetry: (error, attempt, maxRetries) => {
          console.warn(`Database connection error when getting farm by subdomain (attempt ${attempt}/${maxRetries}):`, error.message);
        }
      }
    );

    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    return res.status(200).json({
      id: farm.id,
      name: farm.name,
      subdomain: farm.subdomain,
      custom_login_text: farm.custom_login_text,
      custom_login_logo: farm.custom_login_logo,
      customer_portal_enabled: farm.customer_portal_enabled
    });
  } catch (error) {
    console.error('Error getting farm by subdomain:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all farms for a user
export const getFarmsByUserId = async (req, res) => {
  try {
    const { userId } = req.params;

    // If userId is not provided, use the authenticated user's ID
    const userIdToUse = userId || req.user.id;

    // Find all UserFarm associations for this user
    const userFarms = await UserFarm.findAll({
      where: { user_id: userIdToUse },
      include: [{
        model: Farm,
        include: [{
          model: SubscriptionPlan,
          attributes: ['name', 'price_monthly', 'price_yearly', 'features']
        }]
      }]
    });

    // Extract farm data and include the UserFarm role
    const farms = userFarms.map(userFarm => {
      const farm = userFarm.Farm.toJSON();
      farm.UserFarm = {
        role: userFarm.role,
        permissions: userFarm.permissions,
        is_approved: userFarm.is_approved
      };
      return farm;
    });

    return res.status(200).json({ farms });
  } catch (error) {
    console.error('Error getting farms for user:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get pending farm employees for approval
export const getPendingFarmEmployees = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all unapproved users associated with the farm through UserFarm
    const userFarms = await UserFarm.findAll({
      where: { 
        farm_id: farmId,
        is_approved: false,
        role: 'farm_employee' // Only get farm employees
      },
      include: [{
        model: User,
        attributes: ['id', 'email', 'first_name', 'last_name', 'phone_number', 'created_at', 'user_type']
      }]
    });

    // Extract user data from the userFarms results
    const pendingEmployees = userFarms.map(userFarm => {
      const userData = userFarm.User.toJSON();
      userData.role = userFarm.role;
      userData.user_farm_id = userFarm.id;
      return userData;
    });

    return res.status(200).json({ pendingEmployees });
  } catch (error) {
    console.error('Error getting pending farm employees:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Approve a farm employee
export const approveFarmEmployee = async (req, res) => {
  try {
    const { farmId, userId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Find user to ensure they exist
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Find the UserFarm relationship
    const userFarm = await UserFarm.findOne({
      where: {
        farm_id: farmId,
        user_id: userId
      }
    });

    if (!userFarm) {
      return res.status(404).json({ error: 'User is not associated with this farm' });
    }

    // Approve the user
    await userFarm.update({ is_approved: true });

    // Ensure the user exists in Matrix
    try {
      console.log(`Ensuring user ${userId} exists in Matrix after farm employee approval...`);
      await matrixClientService.ensureMatrixUser(userId);
      console.log(`User ${userId} successfully created/verified in Matrix after farm employee approval`);
    } catch (matrixError) {
      console.error('Error ensuring user exists in Matrix after farm employee approval:', matrixError);
      // Don't fail the operation if Matrix creation fails
    }

    return res.status(200).json({ 
      message: 'Farm employee approved successfully',
      userFarm
    });
  } catch (error) {
    console.error('Error approving farm employee:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Reject a farm employee
export const rejectFarmEmployee = async (req, res) => {
  try {
    const { farmId, userId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Find user to ensure they exist
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Find the UserFarm relationship
    const userFarm = await UserFarm.findOne({
      where: {
        farm_id: farmId,
        user_id: userId
      }
    });

    if (!userFarm) {
      return res.status(404).json({ error: 'User is not associated with this farm' });
    }

    // Delete the UserFarm relationship (reject the user)
    await userFarm.destroy();

    return res.status(200).json({ 
      message: 'Farm employee rejected successfully' 
    });
  } catch (error) {
    console.error('Error rejecting farm employee:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Upload farm logo
export const uploadFarmLogo = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if file was uploaded
    if (!req.files || !req.files.logo) {
      return res.status(400).json({ error: 'No logo file uploaded' });
    }

    const file = req.files.logo;

    // Validate file type (only allow image files)
    const fileBuffer = Buffer.from(file.data);
    const fileTypeValidation = await validateFileType(file.name, fileBuffer);

    if (!fileTypeValidation.valid) {
      return res.status(400).json({ 
        error: 'Invalid file type', 
        reason: fileTypeValidation.reason 
      });
    }

    // Check if it's an image file
    if (!fileTypeValidation.detectedType.startsWith('image/')) {
      return res.status(400).json({ 
        error: 'Invalid file type', 
        reason: 'Only image files are allowed for logos' 
      });
    }

    // Generate storage path
    const storagePath = generateStoragePath(farmId, req.user.id, file.name);

    // Save file to storage
    const savedPath = await saveFile(file.tempFilePath || file.data, storagePath);

    // Generate the URL to the saved file
    // This assumes the files are served from a /uploads path
    const logoUrl = `/uploads/${storagePath}`;

    // Update the farm with the new logo URL
    await farm.update({
      custom_login_logo: logoUrl
    });

    return res.status(200).json({ 
      message: 'Farm logo uploaded successfully',
      logoUrl 
    });
  } catch (error) {
    console.error('Error uploading farm logo:', error);
    return res.status(500).json({ error: error.message });
  }
};
