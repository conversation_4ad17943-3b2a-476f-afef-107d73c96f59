import Invoice from '../models/Invoice.js';
import InvoiceItem from '../models/InvoiceItem.js';
import Customer from '../models/Customer.js';
import Farm from '../models/Farm.js';
import { sequelize } from '../config/database.js';
import { updateInventoryOnProductSale } from './productInventoryController.js';

// Get all invoices for a farm
export const getFarmInvoices = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all invoices for the farm
    const invoices = await Invoice.findAll({
      where: { farm_id: farmId },
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'email']
        }
      ],
      order: [['issue_date', 'DESC']]
    });

    return res.status(200).json({ invoices });
  } catch (error) {
    console.error('Error getting farm invoices:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single invoice by ID
export const getInvoiceById = async (req, res) => {
  try {
    const { invoiceId } = req.params;

    const invoice = await Invoice.findByPk(invoiceId, {
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'email', 'phone', 'address', 'city', 'state', 'zip_code', 'country']
        },
        {
          model: InvoiceItem,
          attributes: ['id', 'description', 'quantity', 'unit_price', 'amount']
        }
      ]
    });

    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    return res.status(200).json({ invoice });
  } catch (error) {
    console.error('Error getting invoice:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new invoice
export const createInvoice = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      farmId, 
      customerId, 
      invoiceNumber, 
      issueDate, 
      dueDate, 
      status, 
      notes,
      items 
    } = req.body;

    // Validate required fields
    if (!farmId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    if (!invoiceNumber) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Invoice number is required' });
    }

    if (!issueDate) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Issue date is required' });
    }

    if (!dueDate) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Due date is required' });
    }

    if (!items || !Array.isArray(items) || items.length === 0) {
      await transaction.rollback();
      return res.status(400).json({ error: 'At least one invoice item is required' });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // If customer ID is provided, ensure it exists
    if (customerId) {
      const customer = await Customer.findOne({
        where: { 
          id: customerId,
          farm_id: farmId
        }
      });

      if (!customer) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Customer not found' });
      }
    }

    // Calculate subtotal and total
    let subtotal = 0;
    for (const item of items) {
      if (!item.description || !item.quantity || !item.unitPrice) {
        await transaction.rollback();
        return res.status(400).json({ error: 'Each item must have a description, quantity, and unit price' });
      }

      const amount = parseFloat(item.quantity) * parseFloat(item.unitPrice);
      subtotal += amount;
      item.amount = amount;
    }

    const taxAmount = req.body.taxAmount || 0;
    const totalAmount = subtotal + parseFloat(taxAmount);

    // Create invoice
    const invoice = await Invoice.create({
      farm_id: farmId,
      customer_id: customerId,
      invoice_number: invoiceNumber,
      issue_date: issueDate,
      due_date: dueDate,
      status: status || 'draft',
      subtotal,
      tax_amount: taxAmount,
      total_amount: totalAmount,
      notes
    }, { transaction });

    // Create invoice items
    const invoiceItems = [];
    for (const item of items) {
      const invoiceItem = await InvoiceItem.create({
        invoice_id: invoice.id,
        description: item.description,
        quantity: item.quantity,
        unit_price: item.unitPrice,
        amount: item.amount,
        product_id: item.productId, // Add product_id if provided
        chart_of_account_id: item.chartOfAccountId
      }, { transaction });

      invoiceItems.push(invoiceItem);
    }

    // Update inventory if invoice is paid or sent
    if (status === 'paid' || status === 'sent') {
      try {
        console.log('Updating inventory for invoice items...');
        const insufficientItems = [];

        // First validate all inventory items
        for (const invoiceItem of invoiceItems) {
          if (invoiceItem.product_id) {
            const result = await updateInventoryOnProductSale(invoiceItem, req.user?.id || null, transaction);
            if (!result.success) {
              if (result.insufficientItems) {
                insufficientItems.push(...result.insufficientItems);
              }
            }
          }
        }

        // If there are insufficient items, roll back and return error
        if (insufficientItems.length > 0) {
          await transaction.rollback();
          return res.status(400).json({ 
            error: 'Insufficient inventory for one or more products', 
            insufficientItems 
          });
        }
      } catch (error) {
        console.error('Error updating inventory:', error);
        await transaction.rollback();
        return res.status(500).json({ error: 'Error updating inventory: ' + error.message });
      }
    }

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Invoice created successfully',
      invoice: {
        ...invoice.toJSON(),
        items: invoiceItems
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating invoice:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update an invoice
export const updateInvoice = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { invoiceId } = req.params;
    const { 
      customerId, 
      invoiceNumber, 
      issueDate, 
      dueDate, 
      status, 
      notes,
      items,
      taxAmount 
    } = req.body;

    // Find invoice to ensure it exists
    const invoice = await Invoice.findByPk(invoiceId, {
      include: [
        {
          model: InvoiceItem
        }
      ]
    });

    if (!invoice) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // If customer ID is provided, ensure it exists
    if (customerId) {
      const customer = await Customer.findOne({
        where: { 
          id: customerId,
          farm_id: invoice.farm_id
        }
      });

      if (!customer) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Customer not found' });
      }
    }

    // Update invoice items if provided
    if (items && Array.isArray(items)) {
      // Delete existing items
      await InvoiceItem.destroy({
        where: { invoice_id: invoiceId },
        transaction
      });

      // Calculate new subtotal
      let subtotal = 0;
      for (const item of items) {
        if (!item.description || !item.quantity || !item.unitPrice) {
          await transaction.rollback();
          return res.status(400).json({ error: 'Each item must have a description, quantity, and unit price' });
        }

        const amount = parseFloat(item.quantity) * parseFloat(item.unitPrice);
        subtotal += amount;
        item.amount = amount;
      }

      // Create new items
      const invoiceItems = [];
      for (const item of items) {
        const invoiceItem = await InvoiceItem.create({
          invoice_id: invoice.id,
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unitPrice,
          amount: item.amount,
          product_id: item.productId, // Add product_id if provided
          chart_of_account_id: item.chartOfAccountId
        }, { transaction });

        invoiceItems.push(invoiceItem);
      }

      // Update inventory if invoice is paid or sent
      const newStatus = status || invoice.status;
      if (newStatus === 'paid' || newStatus === 'sent') {
        try {
          console.log('Updating inventory for invoice items...');
          const insufficientItems = [];

          // First validate all inventory items
          for (const invoiceItem of invoiceItems) {
            if (invoiceItem.product_id) {
              const result = await updateInventoryOnProductSale(invoiceItem, req.user?.id || null, transaction);
              if (!result.success) {
                if (result.insufficientItems) {
                  insufficientItems.push(...result.insufficientItems);
                }
              }
            }
          }

          // If there are insufficient items, roll back and return error
          if (insufficientItems.length > 0) {
            await transaction.rollback();
            return res.status(400).json({ 
              error: 'Insufficient inventory for one or more products', 
              insufficientItems 
            });
          }
        } catch (error) {
          console.error('Error updating inventory:', error);
          await transaction.rollback();
          return res.status(500).json({ error: 'Error updating inventory: ' + error.message });
        }
      }

      // Update invoice with new totals
      const newTaxAmount = taxAmount !== undefined ? parseFloat(taxAmount) : invoice.tax_amount;
      const totalAmount = subtotal + newTaxAmount;

      await invoice.update({
        subtotal,
        tax_amount: newTaxAmount,
        total_amount: totalAmount
      }, { transaction });
    }

    // Update other invoice fields
    await invoice.update({
      customer_id: customerId !== undefined ? customerId : invoice.customer_id,
      invoice_number: invoiceNumber || invoice.invoice_number,
      issue_date: issueDate || invoice.issue_date,
      due_date: dueDate || invoice.due_date,
      status: status || invoice.status,
      notes: notes !== undefined ? notes : invoice.notes
    }, { transaction });

    await transaction.commit();

    // Fetch updated invoice with items
    const updatedInvoice = await Invoice.findByPk(invoiceId, {
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'email']
        },
        {
          model: InvoiceItem
        }
      ]
    });

    return res.status(200).json({ 
      message: 'Invoice updated successfully',
      invoice: updatedInvoice
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating invoice:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete an invoice
export const deleteInvoice = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { invoiceId } = req.params;

    // Find invoice to ensure it exists
    const invoice = await Invoice.findByPk(invoiceId);
    if (!invoice) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Delete invoice (this will cascade delete all related items)
    await invoice.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({ 
      message: 'Invoice deleted successfully' 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting invoice:', error);
    return res.status(500).json({ error: error.message });
  }
};
