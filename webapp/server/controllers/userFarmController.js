import { sequelize } from '../config/database.js';
import User from '../models/User.js';
import Farm from '../models/Farm.js';
import UserFarm from '../models/UserFarm.js';
import Role from '../models/Role.js';
import matrixClientService from '../services/matrixClientService.js';

// Helper function to find or create a role by name and farm ID
const findOrCreateRoleByName = async (roleName, farmId, transaction) => {
  try {
    // First, try to find an existing role for this farm with this name
    let role = await Role.findOne({
      where: {
        name: roleName,
        farm_id: farmId
      },
      transaction
    });

    // If role exists, return it
    if (role) {
      return role;
    }

    // If no farm-specific role exists, try to find a global role with this name
    role = await Role.findOne({
      where: {
        name: roleName,
        farm_id: null
      },
      transaction
    });

    // If global role exists, return it
    if (role) {
      return role;
    }

    // If no role exists at all, create a new one for this farm
    console.log(`Creating new role '${roleName}' for farm ${farmId}`);
    role = await Role.create({
      name: roleName,
      farm_id: farmId,
      description: `${roleName} role for farm ${farmId}`,
      is_system_role: true
    }, { transaction });

    return role;
  } catch (error) {
    console.error(`Error finding or creating role '${roleName}' for farm ${farmId}:`, error);
    throw error;
  }
};

// Create a new UserFarm association
export const createUserFarm = async (req, res) => {
  try {
    const { user_id, farm_id, role, is_approved = false, permissions = null } = req.body;

    // Validate required fields
    if (!user_id || !farm_id) {
      return res.status(400).json({ error: 'User ID and Farm ID are required' });
    }

    // Check if user exists
    const user = await User.findByPk(user_id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if farm exists
    const farm = await Farm.findByPk(farm_id);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if association already exists
    const existingUserFarm = await UserFarm.findOne({
      where: {
        user_id,
        farm_id
      }
    });

    if (existingUserFarm) {
      return res.status(400).json({ error: 'User is already associated with this farm' });
    }

    // Create the UserFarm association
    const roleName = role || 'farm_employee';

    // Find or create the role
    const userRole = await findOrCreateRoleByName(roleName, farm_id);

    const userFarm = await UserFarm.create({
      user_id,
      farm_id,
      role: roleName,
      role_id: userRole.id,
      is_approved,
      permissions
    });

    // Update menu preferences based on the assigned role
    try {
      console.log(`Updating menu preferences based on ${roleName} role`);
      // Import the menu utility function
      const { updateMenuPreferencesByRole } = await import('../utils/menuUtils.js');

      // Update menu preferences with the utility function
      await updateMenuPreferencesByRole(user_id, userRole);
      console.log('Menu preferences updated with role-specific items');
    } catch (menuUpdateError) {
      console.error('Error updating menu preferences with role-specific items:', menuUpdateError);
      // Don't fail the operation if this fails
    }

    // Ensure the user exists in Matrix
    try {
      console.log(`Ensuring user ${user_id} exists in Matrix after farm association...`);
      await matrixClientService.ensureMatrixUser(user_id);
      console.log(`User ${user_id} successfully created/verified in Matrix after farm association`);
    } catch (matrixError) {
      console.error('Error ensuring user exists in Matrix after farm association:', matrixError);
      // Don't fail the operation if Matrix creation fails
    }

    return res.status(201).json({
      message: 'User associated with farm successfully',
      userFarm
    });
  } catch (error) {
    console.error('Error creating UserFarm association:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a UserFarm association
export const updateUserFarm = async (req, res) => {
  try {
    const { userFarmId } = req.params;
    const { role, is_approved, permissions } = req.body;

    // Find the UserFarm association
    const userFarm = await UserFarm.findByPk(userFarmId);
    if (!userFarm) {
      return res.status(404).json({ error: 'UserFarm association not found' });
    }

    // Check if role is being updated
    const isRoleUpdated = role !== undefined && role !== userFarm.role;

    // Check if approval status is being updated
    const isApprovalUpdated = is_approved !== undefined && is_approved !== userFarm.is_approved;

    // Update the UserFarm association
    await userFarm.update({
      role: role !== undefined ? role : userFarm.role,
      is_approved: is_approved !== undefined ? is_approved : userFarm.is_approved,
      permissions: permissions !== undefined ? permissions : userFarm.permissions
    });

    // If role is updated, update menu preferences
    if (isRoleUpdated) {
      try {
        // Get the new role
        const newRole = await findOrCreateRoleByName(role, userFarm.farm_id);

        console.log(`Updating menu preferences based on new role ${role}`);
        // Import the menu utility function
        const { updateMenuPreferencesByRole } = await import('../utils/menuUtils.js');

        // Update menu preferences with the utility function
        await updateMenuPreferencesByRole(userFarm.user_id, newRole);
        console.log('Menu preferences updated with role-specific items');
      } catch (menuUpdateError) {
        console.error('Error updating menu preferences with role-specific items:', menuUpdateError);
        // Don't fail the operation if this fails
      }
    }

    // If user is being approved, ensure they exist in Matrix
    if (isApprovalUpdated && userFarm.is_approved) {
      try {
        console.log(`Ensuring user ${userFarm.user_id} exists in Matrix after farm association update...`);
        await matrixClientService.ensureMatrixUser(userFarm.user_id);
        console.log(`User ${userFarm.user_id} successfully created/verified in Matrix after farm association update`);
      } catch (matrixError) {
        console.error('Error ensuring user exists in Matrix after farm association update:', matrixError);
        // Don't fail the operation if Matrix creation fails
      }
    }

    return res.status(200).json({
      message: 'UserFarm association updated successfully',
      userFarm
    });
  } catch (error) {
    console.error('Error updating UserFarm association:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a UserFarm association
export const deleteUserFarm = async (req, res) => {
  try {
    const { userFarmId } = req.params;

    // Find the UserFarm association
    const userFarm = await UserFarm.findByPk(userFarmId);
    if (!userFarm) {
      return res.status(404).json({ error: 'UserFarm association not found' });
    }

    // Delete the UserFarm association
    await userFarm.destroy();

    return res.status(200).json({
      message: 'UserFarm association deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting UserFarm association:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get UserFarm associations by user ID
export const getUserFarmsByUserId = async (req, res) => {
  try {
    const { userId } = req.params;

    // Find all UserFarm associations for this user
    const userFarms = await UserFarm.findAll({
      where: { user_id: userId },
      include: [{
        model: Farm,
        attributes: ['id', 'name', 'subdomain']
      }]
    });

    return res.status(200).json({ userFarms });
  } catch (error) {
    console.error('Error getting UserFarm associations by user ID:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get UserFarm associations by farm ID
export const getUserFarmsByFarmId = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find all UserFarm associations for this farm
    const userFarms = await UserFarm.findAll({
      where: { farm_id: farmId },
      include: [{
        model: User,
        attributes: ['id', 'email', 'first_name', 'last_name', 'phone_number', 'user_type', 'created_at']
      }]
    });

    return res.status(200).json({ userFarms });
  } catch (error) {
    console.error('Error getting UserFarm associations by farm ID:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a specific UserFarm by ID
export const getUserFarmById = async (req, res) => {
  try {
    const { id } = req.params;

    // Find the UserFarm association by ID
    const userFarm = await UserFarm.findByPk(id, {
      include: [
        {
          model: User,
          attributes: ['id', 'email', 'first_name', 'last_name', 'phone_number', 'user_type', 'created_at']
        },
        {
          model: Farm,
          attributes: ['id', 'name', 'subdomain']
        },
        {
          model: Role,
          attributes: ['id', 'name', 'description', 'permissions']
        }
      ]
    });

    if (!userFarm) {
      return res.status(404).json({ 
        message: "The requested resource could not be found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    return res.status(200).json({ userFarm });
  } catch (error) {
    console.error('Error getting UserFarm by ID:', error);
    return res.status(500).json({ error: error.message });
  }
};
