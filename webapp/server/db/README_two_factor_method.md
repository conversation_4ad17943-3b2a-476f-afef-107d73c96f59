# Two-Factor Authentication Method Column Migration

## Overview

This migration adds the `two_factor_method` column to the `users` table. This column is used to specify the preferred two-factor authentication method for each user, which can be either an authenticator app or SMS.

## Purpose

The `two_factor_method` column is required by the User model but was missing from the database schema. This migration ensures that the database schema matches the model definition, allowing the application to properly store and retrieve the user's preferred 2FA method.

## Column Details

- **Name**: `two_factor_method`
- **Type**: ENUM ('app', 'sms')
- **Nullable**: Yes
- **Default**: NULL
- **Description**: Preferred 2FA method: authenticator app or SMS

## Migration Files

1. **SQL Migration**: `add_two_factor_method_column.sql`
   - Creates the ENUM type `two_factor_method_enum` if it doesn't exist
   - Checks if the column exists with a different type and drops it if needed
   - Adds the column with the ENUM type
   - Adds a comment to the column

2. **Migration Script**: `run_two_factor_method_migration.js`
   - Executes the SQL migration
   - Verifies that the column was added successfully

## How to Apply

To apply this migration, run the following command from the project root:

```bash
node server/scripts/run_two_factor_method_migration.js
```

## Verification

After running the migration, you can verify that the column was added correctly by:

1. Checking the output of the migration script, which should indicate success
2. Querying the database directly:

```sql
SELECT column_name, data_type, udt_name 
FROM information_schema.columns 
WHERE table_schema = 'site' 
AND table_name = 'users' 
AND column_name = 'two_factor_method';
```

## Related Code

The `two_factor_method` column is used in the User model (`server/models/User.js`) and in the authentication controller (`server/controllers/authController.js`) to manage two-factor authentication preferences.