-- Add sample getting started tasks to the database
-- This migration adds initial tasks for the getting started widget

-- Set the schema
SET search_path TO site, public;

-- Check if the getting_started_tasks table exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'site'
        AND table_name = 'getting_started_tasks'
    ) THEN
        -- Only insert tasks if the table is empty
        IF NOT EXISTS (SELECT 1 FROM site.getting_started_tasks LIMIT 1) THEN
            -- Insert sample getting started tasks
            INSERT INTO site.getting_started_tasks (id, title, description, link_path, icon, "order", is_active, user_type, created_at, updated_at)
            VALUES
                (gen_random_uuid(), 'Complete Your Profile', 'Add your farm details and contact information to personalize your experience.', '/settings/profile', 'user', 1, true, 'all', NOW(), NOW()),
                (gen_random_uuid(), 'Add Your First Field', 'Create your first field to start tracking crops and activities.', '/fields/new', 'map', 2, true, 'all', NOW(), NOW()),
                (gen_random_uuid(), 'Set Up Inventory', 'Add your inventory items to track supplies and equipment.', '/inventory', 'box', 3, true, 'all', NOW(), NOW()),
                (gen_random_uuid(), 'Connect Financial Accounts', 'Link your bank accounts to track expenses and income.', '/finances/accounts', 'dollar', 4, true, 'all', NOW(), NOW()),
                (gen_random_uuid(), 'Explore Reports', 'Check out the reporting tools to gain insights into your farm operations.', '/reports', 'chart', 5, true, 'all', NOW(), NOW());

            RAISE NOTICE 'Sample getting started tasks added successfully.';
        ELSE
            RAISE NOTICE 'Getting started tasks already exist. No new tasks added.';
        END IF;
    ELSE
        RAISE NOTICE 'The getting_started_tasks table does not exist. Migration skipped.';
    END IF;
END $$;

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
        INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at)
        VALUES (
            gen_random_uuid(), 
            'add_getting_started_tasks', 
            'webapp/server/db/migrations/add_getting_started_tasks.sql',
            (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
            NOW(),
            NOW(),
            NOW()
        );
    END IF;
END $$;
