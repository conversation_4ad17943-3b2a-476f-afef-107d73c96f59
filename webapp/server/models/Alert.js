import { DataTypes } from 'sequelize';
import User from './User.js';
import Farm from './Farm.js';

const Alert = (sequelize, schema = 'public') => {
  console.log(`Defining Alert model with schema: ${schema}`);

  const Alert = defineModel('Alert', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Alert title'
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: 'Alert message content'
    },
    type: {
      type: DataTypes.ENUM('info', 'warning', 'critical', 'success'),
      allowNull: false,
      defaultValue: 'info',
      comment: 'Alert type/severity'
    },
    source: {
      type: DataTypes.ENUM('system', 'weather', 'equipment', 'inventory', 'financial', 'task', 'crop'),
      allowNull: false,
      defaultValue: 'system',
      comment: 'Source of the alert'
    },
    read: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether the alert has been read'
    },
    farm_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'farms',
        key: 'id'
      },
      comment: 'Farm this alert belongs to'
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: 'User this alert is for (null for farm-wide alerts)'
    },
    related_entity_type: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Type of entity this alert is related to (e.g., equipment, inventory)'
    },
    related_entity_id: {
      type: DataTypes.UUID,
      allowNull: true,
      comment: 'ID of the entity this alert is related to'
    },
    action_url: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'URL for action to take on this alert'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'alerts',
    schema,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  // Associations are defined in associations.js

  return Alert;
};

export default Alert;
