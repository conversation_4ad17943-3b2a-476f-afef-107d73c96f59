import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import SignableDocument from './SignableDocument.js';
import DocumentSigner from './DocumentSigner.js';
import User from './User.js';

const DocumentAuditLog = defineModel('DocumentAuditLog', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  document_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: SignableDocument,
      key: 'id'
    }
  },
  signer_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: DocumentSigner,
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: User,
      key: 'id'
    }
  },
  event_type: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  event_details: {
    type: DataTypes.JSONB,
    allowNull: true
  },
  ip_address: {
    type: DataTypes.STRING(45),
    allowNull: true
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'document_audit_logs',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false,
  indexes: [
    {
      name: 'document_audit_logs_document_id_idx',
      fields: ['document_id']
    },
    {
      name: 'document_audit_logs_signer_id_idx',
      fields: ['signer_id']
    },
    {
      name: 'document_audit_logs_user_id_idx',
      fields: ['user_id']
    },
    {
      name: 'document_audit_logs_event_type_idx',
      fields: ['event_type']
    },
    {
      name: 'document_audit_logs_created_at_idx',
      fields: ['created_at']
    }
  ]
});

// Define associations
DocumentAuditLog.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'auditLogDocument' });
DocumentAuditLog.belongsTo(DocumentSigner, { foreignKey: 'signer_id', as: 'signer' });
DocumentAuditLog.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

export default DocumentAuditLog;
