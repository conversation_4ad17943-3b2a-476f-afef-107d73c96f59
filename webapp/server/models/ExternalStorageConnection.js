import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import User from './User.js';
import Farm from './Farm.js';
import dotenv from 'dotenv';

dotenv.config();

const ExternalStorageConnection = defineModel('ExternalStorageConnection', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    },
    comment: 'Replaced tenant_id as part of migration from tenant to farm'
  },
  provider: {
    type: DataTypes.STRING(50),
    allowNull: false,
    validate: {
      isIn: [['google_drive', 'dropbox']]
    },
    comment: 'External storage provider (google_drive, dropbox)'
  },
  access_token: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  refresh_token: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Some providers may not use refresh tokens'
  },
  token_expires_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Expiration date of the access token'
  },
  provider_user_id: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'User ID in the provider system'
  },
  provider_email: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'User email in the provider system'
  },
  provider_display_name: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'User display name in the provider system'
  },
  status: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'active',
    validate: {
      isIn: [['active', 'inactive', 'error']]
    },
    comment: 'Status of the connection (active, inactive, error)'
  },
  last_sync_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Timestamp of the last successful data sync'
  },
  additional_data: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Additional provider-specific data'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'external_storage_connections',

  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['user_id', 'farm_id', 'provider'],
      name: 'external_storage_connections_user_farm_provider_idx'
    }
  ]
});

// Define associations
ExternalStorageConnection.belongsTo(User, { foreignKey: 'user_id' });
User.hasMany(ExternalStorageConnection, { foreignKey: 'user_id' });

ExternalStorageConnection.belongsTo(Farm, { foreignKey: 'farm_id' });
Farm.hasMany(ExternalStorageConnection, { foreignKey: 'farm_id' });

export default ExternalStorageConnection;
