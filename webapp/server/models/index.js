// Import schema utilities
import { setSchemaCallback, getSchema, resetSchemaCallback, createFarmSchemaCallback } from '../utils/schemaUtils.js';
import { defineModel } from '../utils/modelUtils.js';

// Import models directly
import Farm from './Farm.js';
import User from './User.js';
import FarmGrant from './FarmGrant.js';
import Grant from './Grant.js';
import Subsidy from './Subsidy.js';
import InventoryItem from './InventoryItem.js';
import InventoryCategory from './InventoryCategory.js';
import InventoryTransaction from './InventoryTransaction.js';
import Product from './Product.js';
import SeedProduct from './SeedProduct.js';
import ChemicalProduct from './ChemicalProduct.js';
import Integration from './Integration.js';
import DashboardLayout from './DashboardLayout.js';
import FarmDashboardLayout from './FarmDashboardLayout.js';
import GlobalDashboardLayout from './GlobalDashboardLayout.js';
import ExternalStorageConnection from './ExternalStorageConnection.js';
import Document from './Document.js';
import DocumentFolder from './DocumentFolder.js';
import DocumentPermission from './DocumentPermission.js';
import SignableDocument from './SignableDocument.js';
import DocumentSigner from './DocumentSigner.js';
import DocumentSignature from './DocumentSignature.js';
import DocumentField from './DocumentField.js';
import DocumentAuditLog from './DocumentAuditLog.js';
import Equipment from './Equipment.js';
import EquipmentSharing from './EquipmentSharing.js';
import EquipmentTelematics from './EquipmentTelematics.js';
import IoTDevice from './IoTDevice.js';
import IoTData from './IoTData.js';
import IsobusData from './IsobusData.js';
import Livestock from './Livestock.js';
import LivestockGroup from './LivestockGroup.js';
import Crop from './Crop.js';
import CropActivity from './CropActivity.js';
import CropType from './CropType.js';
import Harvest from './Harvest.js';
import Customer from './Customer.js';
import Invoice from './Invoice.js';
import InvoiceItem from './InvoiceItem.js';
import Order from './Order.js';
import OrderItem from './OrderItem.js';
import ProductInventory from './ProductInventory.js';
import ServiceProvider from './ServiceProvider.js';
import ServiceRequest from './ServiceRequest.js';
import Supplier from './Supplier.js';
import RolePermission from './RolePermission.js';
import Transaction from './Transaction.js';
import Vet from './Vet.js';
import Vendor from './Vendor.js';
import Field from './Field.js';
import Employee from './Employee.js';
import TimeEntry from './TimeEntry.js';
import TimeOffRequest from './TimeOffRequest.js';
import PayStub from './PayStub.js';
import Expense from './Expense.js';
import SubscriptionPlan from './SubscriptionPlan.js';
import SubscriptionTransaction from './SubscriptionTransaction.js';
import FeatureUsage from './FeatureUsage.js';
import UserFarm from './UserFarm.js';
import FAQ from './FAQ.js';
// Import API data models
import ApiProvider from './ApiProvider.js';
import ApiEndpoint from './ApiEndpoint.js';
import ApiRequest from './ApiRequest.js';
import ApiCache from './ApiCache.js';
import ApiAnalytics from './ApiAnalytics.js';

// Import the association setup function
// Note: setupAssociations is now imported and called only in server/index.js
// to avoid duplicate associations

// Export models individually (not using star export)
export {
  // Schema utilities
  setSchemaCallback,
  getSchema,
  resetSchemaCallback,
  createFarmSchemaCallback,
  defineModel,

  // Models
  Farm,
  User,
  FarmGrant,
  Grant,
  Subsidy,
  InventoryItem,
  InventoryCategory,
  InventoryTransaction,
  Product,
  SeedProduct,
  ChemicalProduct,
  Integration,
  DashboardLayout,
  FarmDashboardLayout,
  GlobalDashboardLayout,
  ExternalStorageConnection,
  Document,
  DocumentFolder,
  DocumentPermission,
  SignableDocument,
  DocumentSigner,
  DocumentSignature,
  DocumentField,
  DocumentAuditLog,
  Equipment,
  EquipmentSharing,
  EquipmentTelematics,
  IoTDevice,
  IoTData,
  IsobusData,
  Livestock,
  LivestockGroup,
  Crop,
  CropActivity,
  CropType,
  Harvest,
  Customer,
  Invoice,
  InvoiceItem,
  Order,
  OrderItem,
  ProductInventory,
  ServiceProvider,
  ServiceRequest,
  Supplier,
  RolePermission,
  Transaction,
  Vet,
  Field,
  Employee,
  TimeEntry,
  TimeOffRequest,
  PayStub,
  Expense,
  SubscriptionPlan,
  SubscriptionTransaction,
  FeatureUsage,
  UserFarm,
  Vendor,
  FAQ,
  // API data models
  ApiProvider,
  ApiEndpoint,
  ApiRequest,
  ApiCache,
  ApiAnalytics
};
