import express from 'express';
import {
  getCustomerInvoices,
  getCustomerInvoiceById,
  payInvoice
} from '../controllers/customerInvoiceController.js';
import { authenticateCustomer } from '../middleware/customerAuthMiddleware.js';

const router = express.Router();

// All routes require customer authentication
router.use(authenticateCustomer);

// Get all invoices for the authenticated customer
router.get('/', getCustomerInvoices);

// Get a single invoice by ID for the authenticated customer
router.get('/:invoiceId', getCustomerInvoiceById);

// Pay an invoice
router.post('/:invoiceId/pay', payInvoice);

export default router;