import express from 'express';
import { 
  getGrants, 
  getGrant, 
  createGrant, 
  updateGrant, 
  deleteGrant,
  getSubsidies,
  getSubsidy,
  createSubsidy,
  updateSubsidy,
  deleteSubsidy
} from '../controllers/grantSubsidyController.js';
import { authenticateToken } from '../middleware/authMiddleware.js';
import { checkFarmAccess } from '../middleware/farmAccessMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Grant routes
router.get('/farms/:farmId/grants', checkFarmAccess, getGrants);
router.get('/grants/:grantId', checkFarmAccess, getGrant);
router.post('/farms/:farmId/grants', checkFarmAccess, createGrant);
router.put('/grants/:grantId', checkFarmAccess, updateGrant);
router.delete('/grants/:grantId', checkFarmAccess, deleteGrant);

// Subsidy routes
router.get('/farms/:farmId/subsidies', checkFarmAccess, getSubsidies);
router.get('/subsidies/:subsidyId', checkFarmAccess, getSubsidy);
router.post('/farms/:farmId/subsidies', checkFarmAccess, createSubsidy);
router.put('/subsidies/:subsidyId', checkFarmAccess, updateSubsidy);
router.delete('/subsidies/:subsidyId', checkFarmAccess, deleteSubsidy);

export default router;