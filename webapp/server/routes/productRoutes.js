import express from 'express';
import {
  getFarmProducts,
  getFarmProductsByQuery,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  createProductFromCrop,
  createProductFromLivestock,
  createProductFromEquipment
} from '../controllers/productController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get all products for a farm with query parameter
router.get('/', authenticate, getFarmProductsByQuery);

// Get all products for a farm
router.get('/farm/:farmId', authenticate, getFarmProducts);

// Get a single product by ID
router.get('/:productId', authenticate, getProductById);

// Create a new product
router.post('/', authenticate, createProduct);

// Update a product
router.put('/:productId', authenticate, updateProduct);

// Delete a product
router.delete('/:productId', authenticate, deleteProduct);

// Create a product from a crop
router.post('/from-crop/:cropId', authenticate, createProductFromCrop);

// Create a product from livestock
router.post('/from-livestock/:livestockId', authenticate, createProductFromLivestock);

// Create a product from equipment
router.post('/from-equipment/:equipmentId', authenticate, createProductFromEquipment);

export default router;
