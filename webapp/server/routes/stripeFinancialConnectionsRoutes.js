import express from 'express';
import {
  createFinancialConnectionsSession,
  handleFinancialConnectionsCallback,
  getTransactions,
  getAccountBalances,
  syncAllTransactions,
  getFinancialConnections
} from '../controllers/stripeFinancialConnectionsController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Create a Financial Connections Session
router.post('/create-session', authenticate, createFinancialConnectionsSession);

// Handle Financial Connections Session completion
router.post('/callback', authenticate, handleFinancialConnectionsCallback);

// Get transactions for a specific account
router.get('/transactions', getTransactions);

// Get account balances
router.get('/accounts/:connectionId', getAccountBalances);

// Sync transactions for all financial connections
router.post('/sync/:farmId', syncAllTransactions);

// Get all financial connections for a farm
router.get('/connections/:farmId', getFinancialConnections);

export default router;