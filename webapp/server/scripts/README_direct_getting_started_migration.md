# Direct Getting Started Migration Script

This script directly executes the SQL migrations for the Getting Started feature using the Sequelize connection, rather than relying on npm scripts. It provides more direct control over the migration process and can help resolve issues with the Getting Started widget being blank.

## What it does

The script performs the following actions:

1. Reads the SQL files for creating the Getting Started tables, adding tasks, and ensuring tasks exist
2. Executes the SQL within a transaction to ensure atomicity
3. Verifies that the tables exist and contain data
4. If the `getting_started_tasks` table is empty, it forces the insertion of tasks

## When to use

Use this script if:

1. The Getting Started widget is blank and not showing any content
2. You've already run the regular migrations (`npm run migrate:getting-started`) but they didn't resolve the issue
3. You suspect that the tables exist but are empty or not properly populated

## How to use

Run the script with Node.js:

```bash
node server/scripts/direct_getting_started_migration.js
```

Or use npm:

```bash
npm run migrate:getting-started:direct
```

## Troubleshooting

If you encounter any errors when running the script, check the following:

1. Make sure the database connection is properly configured in your `.env` file
2. Check that the SQL files exist in the `server/db/migrations` directory
3. Look for any error messages in the console output

If the script runs successfully but the Getting Started widget is still blank, check the browser console for any client-side errors and the server logs for any API errors.