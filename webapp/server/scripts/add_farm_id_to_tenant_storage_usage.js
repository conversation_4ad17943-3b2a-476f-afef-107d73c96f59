import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function addFarmIdToTenantStorageUsage() {
  try {
    console.log('Starting migration: Adding farm_id to tenant_storage_usage table...');
    
    // Define the migration file
    const migrationFile = 'add_farm_id_to_tenant_storage_usage.sql';
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../db', migrationFile);
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    await sequelize.query(sql);
    
    console.log(`Migration ${migrationFile} completed successfully`);
    console.log('tenant_storage_usage table now has farm_id column properly set up');
  } catch (error) {
    console.error('Error running migration:', error);
    process.exit(1);
  }
}

// Run the function
addFarmIdToTenantStorageUsage()
  .then(() => {
    console.log('Migration completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });