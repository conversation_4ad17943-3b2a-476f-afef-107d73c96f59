<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="color-scheme" content="light">
  <meta name="supported-color-schemes" content="light">
  <title>Invoice Payment Reminder</title>
  <style>
    :root {
      color-scheme: light;
      supported-color-schemes: light;
    }
    html {
      background-color: #ffffff !important;
    }
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333 !important;
      margin: 0;
      padding: 0;
      background-color: #ffffff !important;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #ffffff;
    }
    .header {
      background-color: #0ea5e9;
      padding: 20px;
      text-align: center;
    }
    .logo {
      max-width: 200px;
      margin: 0 auto;
      display: block;
    }
    .header h1 {
      color: white;
      margin: 10px 0 0 0;
    }
    .content {
      padding: 20px;
      background-color: #f9f9f9;
    }
    .footer {
      text-align: center;
      padding: 10px;
      font-size: 12px;
      color: #666;
    }
    .button {
      display: inline-block;
      background-color: #0ea5e9;
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 5px;
      margin-top: 20px;
    }
    .invoice-details {
      background-color: #f0f9ff;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
      border-left: 4px solid #0ea5e9;
    }
    .amount {
      font-size: 24px;
      font-weight: bold;
      color: #0ea5e9;
    }
    .overdue {
      color: #ef4444;
      font-weight: bold;
    }
    .accent {
      color: #14b8a6;
    }
    .secondary {
      color: #8b5cf6;
    }
    .status-box {
      padding: 15px;
      border-radius: 5px;
      margin: 15px 0;
    }
    .status-box.overdue {
      background-color: rgba(239, 68, 68, 0.1);
      border-left: 4px solid #ef4444;
    }
    .status-box.upcoming {
      background-color: rgba(139, 92, 246, 0.1);
      border-left: 4px solid #8b5cf6;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="https://app.nxtacre.com/logo.svg" alt="nxtAcre Logo" class="logo">
      <h1>Invoice Payment Reminder</h1>
    </div>
    <div class="content">
      <p>Hello {{customerName}},</p>

      {{#if upcoming}}
      <div class="status-box upcoming">
        <p>This is a friendly reminder that the following invoice is due for payment soon:</p>
      </div>
      {{/if}}

      {{#if overdue}}
      <div class="status-box overdue">
        <p>This is a reminder that the following invoice is <span class="overdue">OVERDUE</span>:</p>
      </div>
      {{/if}}

      <div class="invoice-details">
        <p><strong>Invoice Number:</strong> {{invoiceNumber}}</p>
        <p><strong>Date:</strong> {{invoiceDate}}</p>
        <p><strong>Due Date:</strong> {{dueDate}}</p>
        <p><strong>Amount Due:</strong> <span class="amount">${{amount}}</span></p>
        {{#if overdue}}
        <p><strong>Days Overdue:</strong> <span class="overdue">{{daysOverdue}}</span></p>
        {{/if}}
      </div>

      <p>Please make your payment as soon as possible to maintain your account in good standing.</p>

      <p>You can view and pay your invoice online by clicking the button below:</p>

      <a href="{{invoiceUrl}}" class="button">View and Pay Invoice</a>

      <p>If you have already made this payment, please disregard this reminder.</p>

      <p>If you have any questions about this invoice, please don't hesitate to contact our billing department.</p>

      <p>Thank you for your prompt attention to this matter.</p>

      <p>Best regards,<br>The <span class="accent">nxtAcre</span> Team</p>
    </div>
    <div class="footer">
      <p>&copy; {{year}} nxtAcre. All rights reserved.</p>
      <p>This email was sent to {{email}}.</p>
    </div>
  </div>
</body>
</html>
