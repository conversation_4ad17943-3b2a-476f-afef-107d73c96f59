import React, { useState, useEffect, useRef, ChangeEvent } from 'react';
import { useChat, Message, Attachment } from '../../context/ChatContext';
import { formatDistanceToNow } from 'date-fns';
import { formatMessage } from '../../utils/formatMessage';
import BackIcon from './icons/BackIcon';
import SendIcon from './icons/SendIcon';
import EmojiIcon from './icons/EmojiIcon';
import AttachmentIcon from './icons/AttachmentIcon';
import TaskIcon from './icons/TaskIcon';
import CreateTaskModal from './CreateTaskModal';
import EmojiPicker from './EmojiPicker';
import MessageReactions from './MessageReactions';
import FileAttachment from './FileAttachment';

interface MessageAreaProps {
  conversationId: string;
  onBack: () => void;
  isPinned?: boolean;
}

/**
 * Message area component that displays messages in a conversation
 */
const MessageArea: React.FC<MessageAreaProps> = ({ 
  conversationId, 
  onBack,
  isPinned = false
}) => {
  const { 
    conversations, 
    messages, 
    loadMessages, 
    sendMessage, 
    markAsRead,
    activeConversation,
    setActiveConversation,
    addReaction,
    uploadAttachment,
    sendTypingIndicator,
    typingUsers
  } = useChat();
  const [newMessage, setNewMessage] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Find the conversation
  const conversation = conversations.find(c => c.id === conversationId) || activeConversation;

  // Load messages on mount and when conversation changes
  useEffect(() => {
    if (conversationId) {
      loadMessages(conversationId);

      // Set as active conversation
      if (conversation) {
        setActiveConversation(conversation);
      }
    }
  }, [conversationId, loadMessages, conversation, setActiveConversation]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });

    // Mark messages as read
    if (messages.length > 0) {
      const unreadMessageIds = messages
        .filter(msg => !msg.read_status?.some(status => status.user_id === 'current_user_id' && status.is_read))
        .map(msg => msg.id);

      if (unreadMessageIds.length > 0) {
        markAsRead(conversationId, unreadMessageIds);
      }
    }
  }, [messages, conversationId, markAsRead]);

  // Handle input change and send typing indicator
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setNewMessage(value);

    // Send typing indicator
    if (value.trim() !== '') {
      sendTypingIndicator(conversationId, true);

      // Clear previous timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      // Set timeout to stop typing indicator after 3 seconds of inactivity
      typingTimeoutRef.current = setTimeout(() => {
        sendTypingIndicator(conversationId, false);
      }, 3000);
    } else {
      // If input is empty, stop typing indicator
      sendTypingIndicator(conversationId, false);

      // Clear timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
        typingTimeoutRef.current = null;
      }
    }
  };

  // Handle sending a new message
  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();

    if (newMessage.trim() === '') return;

    // Stop typing indicator when sending message
    sendTypingIndicator(conversationId, false);

    // Clear timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }

    sendMessage(conversationId, newMessage)
      .then(() => {
        setNewMessage('');
      })
      .catch(error => {
        console.error('Error sending message:', error);
      });
  };

  // Handle file upload
  const handleFileUpload = async (e: ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;

    const file = e.target.files[0];
    setIsUploading(true);

    try {
      // Upload the file
      const attachment = await uploadAttachment(conversationId, file);

      // Send a message with the attachment
      await sendMessage(
        conversationId, 
        `Shared a file: ${file.name}`, 
        file.type.startsWith('image/') ? 'image' : 'file'
      );

      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error uploading file:', error);
    } finally {
      setIsUploading(false);
    }
  };

  // Trigger file input click
  const handleAttachmentClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Get conversation name
  const getConversationName = () => {
    if (!conversation) return 'Conversation';

    if (conversation.name) return conversation.name;

    // For direct messages, use the other participant's name
    if (conversation.type === 'direct' && conversation.participants && conversation.participants.length > 0) {
      const otherParticipant = conversation.participants[0];
      return `${otherParticipant.first_name} ${otherParticipant.last_name}`;
    }

    return 'Direct Message';
  };

  // Format message time
  const formatMessageTime = (time: string) => {
    return formatDistanceToNow(new Date(time), { addSuffix: true });
  };

  // Group messages by date
  const groupMessagesByDate = (messages: Message[]) => {
    const groups: { [date: string]: Message[] } = {};

    messages.forEach(message => {
      const date = new Date(message.created_at).toLocaleDateString();
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(message);
    });

    return Object.entries(groups).map(([date, messages]) => ({
      date,
      messages
    }));
  };

  // Check if a message is from the current user
  const isCurrentUserMessage = (message: Message) => {
    // In a real app, compare with the current user ID
    return message.sender_id === 'current_user_id';
  };

  // Handle opening the task creation modal
  const handleOpenTaskModal = (message: Message) => {
    setSelectedMessage(message);
    setIsTaskModalOpen(true);
  };

  // Handle task creation success
  const handleTaskCreated = () => {
    // Show a success message or notification
    console.log('Task created successfully');
  };

  return (
    <div className="flex flex-col w-full h-full">
      {/* Header */}
      <div className="bg-white border-b p-3 flex items-center">
        {!isPinned && (
          <button
            onClick={onBack}
            className="mr-2 text-gray-500 hover:text-gray-700"
            aria-label="Back to conversations"
          >
            <BackIcon size={18} />
          </button>
        )}
        <div className="flex-1">
          <h2 className="text-lg font-semibold">{getConversationName()}</h2>
          {conversation?.participants && (
            <p className="text-xs text-gray-500">
              {conversation.participants.length} participants
            </p>
          )}
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-3 bg-gray-50">
        {groupMessagesByDate(messages).map(group => (
          <div key={group.date} className="mb-4">
            <div className="text-center mb-2">
              <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                {group.date}
              </span>
            </div>

            {group.messages.map(message => (
              <div
                key={message.id}
                className={`mb-2 flex ${isCurrentUserMessage(message) ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs md:max-w-md lg:max-w-lg rounded-lg p-3 ${
                    isCurrentUserMessage(message)
                      ? 'bg-blue-600 text-white rounded-br-none'
                      : 'bg-white border rounded-bl-none'
                  }`}
                >
                  {!isCurrentUserMessage(message) && (
                    <div className="text-xs font-semibold mb-1">
                      {message.sender_first_name} {message.sender_last_name}
                    </div>
                  )}
                  <div 
                    className="break-words" 
                    dangerouslySetInnerHTML={{ __html: formatMessage(message.content) }}
                  />

                  {/* Display attachments if any */}
                  {message.attachments && message.attachments.length > 0 && (
                    <div className="mt-2">
                      {message.attachments.map((attachment) => (
                        <FileAttachment key={attachment.id} attachment={attachment} />
                      ))}
                    </div>
                  )}
                  <div className={`flex justify-between items-center text-xs mt-1 ${isCurrentUserMessage(message) ? 'text-blue-200' : 'text-gray-500'}`}>
                    <div className="flex space-x-2">
                      <button
                        type="button"
                        onClick={() => handleOpenTaskModal(message)}
                        className={`hover:${isCurrentUserMessage(message) ? 'text-white' : 'text-gray-700'}`}
                        title="Create task from this message"
                      >
                        <TaskIcon size={16} />
                      </button>
                    </div>
                    <div>{formatMessageTime(message.created_at)}</div>
                  </div>
                  {/* Message reactions */}
                  <MessageReactions
                    messageId={message.id}
                    conversationId={conversationId}
                    reactions={message.reactions || []}
                    onAddReaction={addReaction}
                  />
                </div>
              </div>
            ))}
          </div>
        ))}
        {/* Typing indicators */}
        {typingUsers.length > 0 && (
          <div className="flex items-center mt-2 mb-1 ml-2">
            <div className="bg-gray-200 text-gray-700 rounded-lg py-1 px-3 text-sm">
              {typingUsers.map(user => {
                // In a real app, you would get the user's name from a users object or API
                const userName = conversation?.participants?.find(p => p.id === user.id)?.first_name || 'Someone';
                return user.is_typing ? `${userName} is typing...` : null;
              }).filter(Boolean).join(', ')}
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message input */}
      <div className="bg-white border-t p-3">
        <form onSubmit={handleSendMessage} className="flex items-center">
          <div className="relative">
            <button
              type="button"
              className="text-gray-500 hover:text-gray-700 mr-2"
              aria-label="Add emoji"
              onClick={() => setShowEmojiPicker(!showEmojiPicker)}
            >
              <EmojiIcon size={20} />
            </button>
            {showEmojiPicker && (
              <EmojiPicker
                onEmojiSelect={(emoji) => {
                  setNewMessage(prev => prev + emoji);
                  setShowEmojiPicker(false);
                }}
                onClose={() => setShowEmojiPicker(false)}
              />
            )}
          </div>
          <div className="relative">
            <button
              type="button"
              className={`text-gray-500 hover:text-gray-700 mr-2 ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
              aria-label="Add attachment"
              onClick={handleAttachmentClick}
              disabled={isUploading}
            >
              <AttachmentIcon size={20} />
            </button>
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              onChange={handleFileUpload}
              disabled={isUploading}
            />
            {isUploading && (
              <div className="absolute -top-8 left-0 bg-white shadow-md rounded px-2 py-1 text-xs">
                Uploading...
              </div>
            )}
          </div>
          <input
            type="text"
            placeholder="Type a message..."
            className="flex-1 border rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={newMessage}
            onChange={handleInputChange}
          />
          <button
            type="submit"
            className="ml-2 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-2"
            aria-label="Send message"
            disabled={newMessage.trim() === ''}
          >
            <SendIcon size={20} />
          </button>
        </form>
      </div>

      {/* Task Creation Modal */}
      {selectedMessage && (
        <CreateTaskModal
          isOpen={isTaskModalOpen}
          onClose={() => setIsTaskModalOpen(false)}
          messageContent={selectedMessage.content}
          onSuccess={handleTaskCreated}
        />
      )}
    </div>
  );
};

export default MessageArea;
