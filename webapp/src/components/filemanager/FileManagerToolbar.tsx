import React from 'react';
import { FileItem, ViewMode } from './FileManagerTypes';

interface FileManagerToolbarProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  onUpload: () => void;
  onCreateFolder: () => void;
  onCreateDocument: () => void;
  onSearch: (query: string) => void;
  searchQuery: string;
  selectedItems: FileItem[];
  onDownload: (item: FileItem) => void;
  onRename: (item: FileItem) => void;
  onDelete: (items: FileItem[]) => void;
  onMove?: (items: FileItem[]) => void;
  onManagePermissions?: (item: FileItem) => void;
  onShare?: (item: FileItem) => void;
  onCopy?: (items: FileItem[]) => void;
  onCut?: (items: FileItem[]) => void;
  onPaste?: () => void;
  canPaste?: boolean;
}

const FileManagerToolbar: React.FC<FileManagerToolbarProps> = ({
  viewMode,
  onViewModeChange,
  onUpload,
  onCreateFolder,
  onCreateDocument,
  onSearch,
  searchQuery,
  selectedItems,
  onDownload,
  onRename,
  onDelete,
  onMove,
  onManagePermissions,
  onCopy,
  onCut,
  onPaste,
  canPaste
}) => {
  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearch(e.target.value);
  };

  // Check if actions should be enabled based on selection
  const canDownload = selectedItems.length === 1 && selectedItems[0].type === 'file';
  const canBulkDownload = selectedItems.length > 1 && selectedItems.every(item => item.type === 'file');
  const canRename = selectedItems.length === 1;
  const canDelete = selectedItems.length > 0;
  const canMove = selectedItems.length > 0;
  const canManagePermissions = selectedItems.length === 1 && onManagePermissions !== undefined;
  const canCopy = selectedItems.length > 0 && onCopy !== undefined;
  const canCut = selectedItems.length > 0 && onCut !== undefined;

  return (
    <div className="border-b border-gray-200 p-4 file-manager-toolbar">
      <div className="flex flex-wrap items-center justify-between gap-4">
        {/* Left side - Actions */}
        <div className="flex items-center space-x-2">
          <button
            onClick={onUpload}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            title="Upload files"
          >
            <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12" />
            </svg>
            Upload
          </button>

          <button
            onClick={onCreateFolder}
            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            title="Create new folder"
          >
            <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 13h6m-3-3v6m-9-6h9m-9 6h9" />
            </svg>
            New Folder
          </button>

          <button
            onClick={onCreateDocument}
            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            title="Create new document"
          >
            <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            New Document
          </button>
          <span className="ml-2 text-xs text-gray-500 italic">
            (Right-click folder/file for more options or drag and drop files below to upload them)
          </span>

          {/* Action buttons on selection have been removed as per requirements */}
        </div>

        {/* Right side - Search and View Mode */}
        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="relative">
            <input
              type="text"
              placeholder="Search files..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="w-64 px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
            />
            <svg
              className="absolute right-3 top-2.5 h-5 w-5 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>

          {/* View Mode Toggle */}
          <div className="flex border border-gray-300 rounded-md overflow-hidden">
            <button
              onClick={() => onViewModeChange('grid')}
              className={`px-3 py-2 ${
                viewMode === 'grid'
                  ? 'bg-primary-100 text-primary-700'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
              title="Grid view"
            >
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
            </button>
            <button
              onClick={() => onViewModeChange('list')}
              className={`px-3 py-2 ${
                viewMode === 'list'
                  ? 'bg-primary-100 text-primary-700'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
              title="List view"
            >
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Selection info */}
      <div className="mt-2 text-sm text-gray-500">
        {selectedItems.length} {selectedItems.length === 1 ? 'item' : 'items'} selected
      </div>
    </div>
  );
};

export default FileManagerToolbar;
