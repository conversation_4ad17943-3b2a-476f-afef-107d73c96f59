import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFarm } from '../../context/FarmContext';
import { toast } from 'react-hot-toast';
import { 
  createComplianceRecord, 
  getComplianceRecords, 
  deleteComplianceRecord,
  uploadComplianceDocument
} from '../../services/laborService';
import Layout from '../../components/Layout';
import { format } from 'date-fns';
import { API_URL } from '../../config';

const ComplianceTracking: React.FC = () => {
  const navigate = useNavigate();
  const { currentFarm } = useFarm();
  const [isLoading, setIsLoading] = useState(false);
  const [records, setRecords] = useState<any[]>([]);
  const [formData, setFormData] = useState({
    recordType: '',
    recordDate: format(new Date(), 'yyyy-MM-dd'),
    expirationDate: '',
    status: 'pending',
    details: '',
    documentUrl: '',
    notes: ''
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadingFile, setUploadingFile] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (currentFarm?.id) {
      fetchRecords();
    }
  }, [currentFarm]);

  const fetchRecords = async () => {
    try {
      setIsLoading(true);
      const data = await getComplianceRecords(Number(currentFarm!.id));
      setRecords(data);
    } catch (error) {
      console.error('Error fetching compliance records:', error);
      toast.error('Failed to fetch compliance records');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleFileUpload = async () => {
    if (!selectedFile || !currentFarm?.id) return;

    try {
      setUploadingFile(true);
      const document = await uploadComplianceDocument(Number(currentFarm.id), selectedFile);

      // Set the document URL in the form data
      setFormData(prev => ({ ...prev, documentUrl: document.file_path }));

      toast.success('Document uploaded successfully');
      setSelectedFile(null);

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error uploading document:', error);
      toast.error('Failed to upload document');
    } finally {
      setUploadingFile(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentFarm?.id) {
      toast.error('Please select a farm first');
      return;
    }

    try {
      setIsLoading(true);
      await createComplianceRecord({
        farmId: Number(currentFarm.id),
        ...formData,
        status: formData.status as 'compliant' | 'non-compliant' | 'pending'
      });

      toast.success('Compliance record added successfully');
      setFormData({
        recordType: '',
        recordDate: format(new Date(), 'yyyy-MM-dd'),
        expirationDate: '',
        status: 'pending',
        details: '',
        documentUrl: '',
        notes: ''
      });
      fetchRecords();
    } catch (error) {
      console.error('Error creating compliance record:', error);
      toast.error('Failed to add compliance record');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this compliance record?')) {
      try {
        setIsLoading(true);
        await deleteComplianceRecord(id);
        toast.success('Compliance record deleted successfully');
        fetchRecords();
      } catch (error) {
        console.error('Error deleting compliance record:', error);
        toast.error('Failed to delete compliance record');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const getStatusLabel = (status: string) => {
    const statuses: Record<string, string> = {
      'compliant': 'Compliant',
      'non-compliant': 'Non-Compliant',
      'pending': 'Pending'
    };
    return statuses[status] || status;
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'compliant': 'bg-green-100 text-green-800',
      'non-compliant': 'bg-red-100 text-red-800',
      'pending': 'bg-yellow-100 text-yellow-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const isExpired = (expirationDate: string) => {
    if (!expirationDate) return false;
    return new Date(expirationDate) < new Date();
  };

  const getRecordTypeOptions = () => {
    return [
      { value: 'labor-permit', label: 'Labor Permit' },
      { value: 'worker-compensation', label: 'Worker Compensation Insurance' },
      { value: 'safety-inspection', label: 'Safety Inspection' },
      { value: 'environmental-compliance', label: 'Environmental Compliance' },
      { value: 'pesticide-license', label: 'Pesticide Applicator License' },
      { value: 'food-safety', label: 'Food Safety Certification' },
      { value: 'organic-certification', label: 'Organic Certification' },
      { value: 'tax-filing', label: 'Tax Filing' },
      { value: 'other', label: 'Other' }
    ];
  };

  const getRecordTypeLabel = (value: string) => {
    const option = getRecordTypeOptions().find(opt => opt.value === value);
    return option ? option.label : value;
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Compliance Tracking</h1>
        <button
          onClick={() => navigate('/labor')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
        >
          Back to Dashboard
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Add Compliance Record</h2>
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="recordType" className="block text-sm font-medium text-gray-700 mb-1">
                Record Type
              </label>
              <select
                id="recordType"
                name="recordType"
                value={formData.recordType}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="">Select Record Type</option>
                {getRecordTypeOptions().map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="recordDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Record Date
                </label>
                <input
                  type="date"
                  id="recordDate"
                  name="recordDate"
                  value={formData.recordDate}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>
              <div>
                <label htmlFor="expirationDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Expiration Date (Optional)
                </label>
                <input
                  type="date"
                  id="expirationDate"
                  name="expirationDate"
                  value={formData.expirationDate}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>

            <div className="mb-4">
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="compliant">Compliant</option>
                <option value="non-compliant">Non-Compliant</option>
                <option value="pending">Pending</option>
              </select>
            </div>

            <div className="mb-4">
              <label htmlFor="details" className="block text-sm font-medium text-gray-700 mb-1">
                Details
              </label>
              <textarea
                id="details"
                name="details"
                value={formData.details}
                onChange={handleInputChange}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
                placeholder="Provide details about this compliance record..."
              />
            </div>

            <div className="mb-4">
              <label htmlFor="documentUpload" className="block text-sm font-medium text-gray-700 mb-1">
                Document Upload (Optional)
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="file"
                  id="documentUpload"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  className="hidden"
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                />
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  className="px-3 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  Choose File
                </button>
                <button
                  type="button"
                  onClick={handleFileUpload}
                  disabled={!selectedFile || uploadingFile}
                  className={`px-3 py-2 rounded focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                    !selectedFile || uploadingFile
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-primary-600 text-white hover:bg-primary-700'
                  }`}
                >
                  {uploadingFile ? 'Uploading...' : 'Upload'}
                </button>
                <span className="text-sm text-gray-500">
                  {selectedFile ? selectedFile.name : 'No file selected'}
                </span>
              </div>
              {formData.documentUrl && (
                <div className="mt-2 flex items-center">
                  <span className="text-sm text-green-600 mr-2">✓ Document uploaded</span>
                  <button
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, documentUrl: '' }))}
                    className="text-xs text-red-600 hover:text-red-800"
                  >
                    Remove
                  </button>
                </div>
              )}
            </div>

            <div className="mb-4">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-400"
            >
              {isLoading ? 'Adding...' : 'Add Compliance Record'}
            </button>
          </form>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Compliance Records</h2>
          {isLoading && <p className="text-gray-500">Loading...</p>}

          {!isLoading && records.length === 0 && (
            <p className="text-gray-500">No compliance records found.</p>
          )}

          {!isLoading && records.length > 0 && (
            <div className="space-y-4">
              {records.map((record) => (
                <div key={record.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        {getRecordTypeLabel(record.record_type)}
                      </h3>
                      <p className="text-sm text-gray-500 mb-2">
                        Date: {new Date(record.record_date).toLocaleDateString()}
                        {record.expiration_date && ` • Expires: ${new Date(record.expiration_date).toLocaleDateString()}`}
                      </p>
                      <span 
                        className={`inline-block px-2 py-1 rounded text-xs font-medium ${
                          isExpired(record.expiration_date) 
                            ? 'bg-red-100 text-red-800' 
                            : getStatusColor(record.status)
                        }`}
                      >
                        {isExpired(record.expiration_date) ? 'Expired' : getStatusLabel(record.status)}
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      {record.document_url && (
                        <a
                          href={`${API_URL}/documents/documents/${record.document_url.split('/').pop()}/download`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-blue-600 hover:text-blue-900"
                        >
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                          </svg>
                          View Document
                        </a>
                      )}
                      <button
                        onClick={() => handleDelete(record.id)}
                        className="inline-flex items-center text-red-600 hover:text-red-900"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Delete
                      </button>
                    </div>
                  </div>
                  <div className="mt-2">
                    <p className="text-sm font-medium text-gray-700">Details:</p>
                    <p className="text-sm text-gray-600">{record.details}</p>
                  </div>
                  {record.notes && (
                    <div className="mt-2">
                      <p className="text-sm font-medium text-gray-700">Notes:</p>
                      <p className="text-sm text-gray-600">{record.notes}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default ComplianceTracking;
